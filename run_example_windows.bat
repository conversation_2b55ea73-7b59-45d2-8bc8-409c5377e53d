@echo off
REM ========================================
REM Windows Batch Script - Super-Resolution Microbubble Velocimetry
REM ========================================
REM
REM Usage:
REM Method 1: Double-click this file (Recommended)
REM Method 2: Command Prompt
REM        cd C:\path\to\project
REM        run_example_windows.bat
REM Method 3: PowerShell
REM        cd C:\path\to\project
REM        .\run_example_windows.bat
REM
REM Note: Make sure to download model and data files to corresponding folders
REM ========================================

echo ========================================
echo Super-Resolution Microbubble Velocimetry - Windows Script
echo ========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found, please install Python 3.8 first
    echo Download: https://www.python.org/downloads/windows/
    pause
    exit /b 1
)

echo Python installed, version info:
python --version

REM Check if virtual environment exists
if not exist "smv_env\Scripts\activate.bat" (
    echo Virtual environment does not exist, creating...
    python -m venv smv_env
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
    echo Virtual environment created successfully
)

REM Activate virtual environment
echo Activating virtual environment...
call smv_env\Scripts\activate.bat

REM Check if requirements.txt exists
if not exist "requirements.txt" (
    echo ERROR: requirements.txt file not found
    pause
    exit /b 1
)

REM Install dependencies
echo Checking and installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo WARNING: Dependency installation may have issues, but continuing...
)

REM Create necessary folders
if not exist "models_pretrained" mkdir models_pretrained
if not exist "data" mkdir data
if not exist "output" mkdir output

echo Folder structure prepared

REM Check if model file exists
if not exist "models_pretrained\SMV_pretrained_weights_mousebrain.pth.tar" (
    echo ========================================
    echo WARNING: Pre-trained model file not found
    echo ========================================
    echo Please download model file from:
    echo https://doi.org/10.7910/DVN/SECUFD
    echo.
    echo Download file: SMV_pretrained_weights_mousebrain.pth.tar
    echo Place in: models_pretrained\
    echo.
    pause
    exit /b 1
)

REM Check if data file exists
if not exist "data\dirfilt_IQData_20220502T105116_3.mat" (
    echo ========================================
    echo WARNING: Example data file not found
    echo ========================================
    echo Please download data file from:
    echo https://doi.org/10.7910/DVN/SECUFD
    echo.
    echo Download file: dirfilt_IQData_20220502T105116_3.mat
    echo Place in: data\
    echo.
    pause
    exit /b 1
)

REM Run inference
echo ========================================
echo Starting inference...
echo ========================================

python inference.py ^
    -m "SMV_pretrained_weights_mousebrain.pth.tar" ^
    -mf ".\models_pretrained\" ^
    -fn "dirfilt_IQData_20220502T105116_3.mat" ^
    -ff ".\data\" ^
    -sp ".\output\example_out.mat" ^
    -w 16 -bd 1 -df 1

if errorlevel 1 (
    echo ========================================
    echo Execution failed!
    echo ========================================
    echo Possible causes:
    echo 1. Missing CUDA support (try installing CPU version PyTorch)
    echo 2. Insufficient memory (try reducing window size -w 8)
    echo 3. Dependency issues (check requirements.txt installation)
    echo.
    echo Please check error messages above
    echo For more help, see "Common Issues" section in README_CN.md
    pause
    exit /b 1
) else (
    echo ========================================
    echo Execution successful!
    echo ========================================
    echo Output file location: output\example_out.mat
    echo.
    echo You can view results using Python or MATLAB:
    echo Python: from scipy.io import loadmat; result = loadmat('output/example_out.mat')
    echo MATLAB: load('output/example_out.mat')
)

echo.
echo Press any key to exit...
pause >nul
