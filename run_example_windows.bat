@echo off
REM Windows批处理脚本 - 运行超分辨率微泡测速示例
REM 使用方法：双击运行或在命令行中执行 run_example_windows.bat

echo ========================================
echo 超分辨率微泡测速系统 - Windows运行脚本
echo ========================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.8
    echo 下载地址：https://www.python.org/downloads/windows/
    pause
    exit /b 1
)

echo Python已安装，版本信息：
python --version

REM 检查虚拟环境是否存在
if not exist "smv_env\Scripts\activate.bat" (
    echo 虚拟环境不存在，正在创建...
    python -m venv smv_env
    if errorlevel 1 (
        echo 错误：创建虚拟环境失败
        pause
        exit /b 1
    )
    echo 虚拟环境创建成功
)

REM 激活虚拟环境
echo 激活虚拟环境...
call smv_env\Scripts\activate.bat

REM 检查requirements.txt是否存在
if not exist "requirements.txt" (
    echo 错误：未找到requirements.txt文件
    pause
    exit /b 1
)

REM 安装依赖（如果需要）
echo 检查并安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo 警告：依赖安装可能有问题，但继续运行...
)

REM 创建必要的文件夹
if not exist "models_pretrained" mkdir models_pretrained
if not exist "data" mkdir data
if not exist "output" mkdir output

echo 文件夹结构已准备完成

REM 检查模型文件是否存在
if not exist "models_pretrained\SMV_pretrained_weights_mousebrain.pth.tar" (
    echo ========================================
    echo 警告：未找到预训练模型文件
    echo ========================================
    echo 请从以下地址下载模型文件：
    echo https://doi.org/10.7910/DVN/SECUFD
    echo.
    echo 下载文件：SMV_pretrained_weights_mousebrain.pth.tar
    echo 放置位置：models_pretrained\
    echo.
    pause
    exit /b 1
)

REM 检查数据文件是否存在
if not exist "data\dirfilt_IQData_20220502T105116_3.mat" (
    echo ========================================
    echo 警告：未找到示例数据文件
    echo ========================================
    echo 请从以下地址下载数据文件：
    echo https://doi.org/10.7910/DVN/SECUFD
    echo.
    echo 下载文件：dirfilt_IQData_20220502T105116_3.mat
    echo 放置位置：data\
    echo.
    pause
    exit /b 1
)

REM 运行推理
echo ========================================
echo 开始运行推理...
echo ========================================

python inference.py ^
    -m "SMV_pretrained_weights_mousebrain.pth.tar" ^
    -mf ".\models_pretrained\" ^
    -fn "dirfilt_IQData_20220502T105116_3.mat" ^
    -ff ".\data\" ^
    -sp ".\output\example_out.mat" ^
    -w 16 -bd 1 -df 1

if errorlevel 1 (
    echo ========================================
    echo 运行失败！
    echo ========================================
    echo 可能的原因：
    echo 1. 缺少CUDA支持（尝试安装CPU版本PyTorch）
    echo 2. 内存不足（尝试减小窗口大小 -w 8）
    echo 3. 依赖包问题（检查requirements.txt安装）
    echo.
    echo 详细错误信息请查看上方输出
    echo 更多帮助请查看 README_CN.md 中的"常见问题解决"部分
    pause
    exit /b 1
) else (
    echo ========================================
    echo 运行成功！
    echo ========================================
    echo 输出文件位置：output\example_out.mat
    echo.
    echo 您可以使用Python或MATLAB查看结果：
    echo Python: from scipy.io import loadmat; result = loadmat('output/example_out.mat')
    echo MATLAB: load('output/example_out.mat')
)

echo.
echo 按任意键退出...
pause >nul
