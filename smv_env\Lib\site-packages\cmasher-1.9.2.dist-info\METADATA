Metadata-Version: 2.3
Name: cmasher
Version: 1.9.2
Summary: Scientific colormaps for making accessible, informative and 'cmashing' plots
Project-URL: Homepage, https://cmasher.readthedocs.io
Project-URL: Documentation, https://cmasher.readthedocs.io
Project-URL: Source Code, https://github.com/1313e/CMasher
Author-email: <PERSON><PERSON> <el<PERSON><PERSON>_van<PERSON><EMAIL>>
License: BSD-3
Keywords: cmasher perceptually uniform sequential colormaps plotting python visualization
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: Matplotlib
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Natural Language :: English
Classifier: Operating System :: MacOS
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: Unix
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Scientific/Engineering :: Visualization
Classifier: Topic :: Utilities
Requires-Python: <4,>=3.10
Requires-Dist: colorspacious>=1.1.0
Requires-Dist: matplotlib>=3.5
Requires-Dist: numpy>=1.21.2
Description-Content-Type: text/x-rst

|PyPI| |conda-forge| |Python| |GitHub| |JOSS|

*CMasher*: Scientific colormaps for making accessible, informative and *cmashing* plots
=======================================================================================
The *CMasher* package provides a collection of scientific colormaps and utility functions to be used by different *Python* packages and projects, mainly in combination with `matplotlib`_, showcased in the `online documentation`_ (where I also describe how to use the colormaps in other languages and applications).
The colormaps in *CMasher* are all designed to be perceptually uniform sequential using the `viscm`_ package; most of them are color-vision deficiency friendly; and they cover a wide range of different color combinations to accommodate for most applications.
It offers several alternatives to commonly used colormaps, like *chroma* and *rainforest* for *jet*; *sunburst* for *hot*; *neutral* for *binary*; and *fusion* and *redshift* for *coolwarm*.
If you cannot find your ideal colormap, then please open an `issue`_, provide the colors and/or style you want, and I will try to create one to your liking!
Let's get rid of all bad colormaps in the world together!

*If you use CMasher for your work, then please star the repo, such that I can keep track of how many users it has and more easily raise awareness of bad colormaps.*
*Additionally, if you use CMasher as part of your workflow in a scientific publication, please consider citing the CMasher paper* (*BibTeX:* ``cmr.get_bibtex``).

.. _issue: https://github.com/1313e/CMasher/issues
.. _online documentation: https://cmasher.readthedocs.io
.. _matplotlib: https://github.com/matplotlib/matplotlib
.. _viscm: https://github.com/matplotlib/viscm

Colormap overview
-----------------
Below is an overview of all the colormaps that are currently in *CMasher* (made with the ``cmr.create_cmap_overview()`` function).
For more information, see the `online documentation`_.

.. image:: https://github.com/1313e/CMasher/raw/master/static/cmap_overview.png
    :width: 100%
    :align: center
    :target: https://cmasher.readthedocs.io
    :alt: CMasher Colormap Overview

In the figure, one can see this wide range of color combinations that *CMasher* has to offer, as I wanted to make sure that *CMasher* has a colormap for everyone.
Because of this, *CMasher*'s sequential colormaps range from single major color maps like *amber*; *ember*; *flamingo*; *freeze*; *gothic*; and *jungle*, to colormaps with high perceptual ranges like *apple*; *chroma*; *torch*; *neon*; and *rainforest*.
The diverging colormaps in *CMasher* have a similar variety, but more importantly, several of them have a black center instead of a white center, like *iceburn*; *redshift*; *watermelon*; and *wildfire*.
Black centered diverging colormaps are quite rare as most researchers are used to white centered ones, even though a black centered diverging colormap can be rather useful in certain cases, like plotting a radial velocity map (the further away from the common center, the higher the velocity in either direction, and thus the corresponding color should be brighter).


Installation & Use
==================
How to install
--------------
*CMasher* can be easily installed directly from `PyPI`_ with::

    $ pip install cmasher

or from `conda-forge`_ with::

    $ conda install -c conda-forge cmasher  # If conda-forge is not set up as a channel
    $ conda install cmasher                 # If conda-forge is set up as a channel

If required, one can also clone the `repository`_ and install *CMasher* manually::

    $ git clone https://github.com/1313e/CMasher
    $ cd CMasher
    $ pip install .

*CMasher* can now be imported as a package with ``import cmasher as cmr``.

Besides Python, *CMasher*'s colormaps can also be accessed in various other languages and applications.
A list of all currently known languages and applications that support *CMasher* can be found in the online documentation `here <https://cmasher.readthedocs.io/user/usage.html#accessing-colormaps>`_.

.. _repository: https://github.com/1313e/CMasher
.. _PyPI: https://pypi.org/project/CMasher
.. _conda-forge: https://anaconda.org/conda-forge/CMasher

Example use
-----------
The colormaps shown above can be accessed by simply importing *CMasher*.
This makes them available in the ``cmasher`` module, in addition to registering them in *matplotlib*'s ``cm`` module (with added ``'cmr.'`` prefix to avoid name clashes).
So, for example, if one were to use the *rainforest* colormap, this could be done with:

.. code:: python

    # Import CMasher to register colormaps
    import cmasher as cmr

    # Import packages for plotting
    import matplotlib as mpl
    import matplotlib.pyplot as plt
    import numpy as np

    # Access rainforest colormap through CMasher or MPL
    cmap = cmr.rainforest                   # CMasher
    cmap = mpl.colormaps['cmr.rainforest']  # MPL

    # Generate some data to plot
    x = np.random.rand(100)
    y = np.random.rand(100)
    z = x**2+y**2

    # Make scatter plot of data with colormap
    plt.scatter(x, y, c=z, cmap=cmap, s=300)
    plt.show()

For other use-cases, including an overview of *CMasher*'s utility functions and how to use *CMasher* in other programming languages and applications, see the `online documentation`_.


.. |PyPI| image:: https://img.shields.io/pypi/v/CMasher.svg?logo=pypi&logoColor=white&label=PyPI
    :target: https://pypi.python.org/pypi/CMasher
    :alt: PyPI - Latest Release
.. |Python| image:: https://img.shields.io/pypi/pyversions/CMasher?logo=python&logoColor=white&label=Python
    :target: https://pypi.python.org/pypi/CMasher
    :alt: PyPI - Python Versions
.. |GitHub| image:: https://img.shields.io/github/actions/workflow/status/1313e/CMasher/.github/workflows/test.yml?branch=dev
    :target: https://github.com/1313e/CMasher/actions
    :alt: GitHub Actions - Build Status

.. |ReadTheDocs| image:: https://img.shields.io/readthedocs/cmasher/latest.svg?logo=read%20the%20docs&logoColor=white&label=Docs
    :target: https://cmasher.readthedocs.io
    :alt: ReadTheDocs - Build Status
.. |JOSS| image:: https://img.shields.io/badge/JOSS-paper-brightgreen
   :target: https://doi.org/10.21105/joss.02004
   :alt: JOSS - Submission Status
.. |conda-forge| image:: https://img.shields.io/conda/vn/conda-forge/cmasher.svg?logo=conda-forge&logoColor=white
    :target: https://anaconda.org/conda-forge/cmasher
    :alt: Conda-Forge - Latest Release
