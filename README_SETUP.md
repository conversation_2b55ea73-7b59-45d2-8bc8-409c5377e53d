# 项目环境配置与复现指南

本文档详细说明如何配置环境并成功运行超分辨率微泡测速项目。

## 系统要求

### 硬件要求
- **推荐配置：** NVIDIA GPU（支持CUDA）用于加速训练和推理
- **最低配置：** CPU（性能较慢但可运行）
- **内存：** 建议8GB以上RAM
- **存储：** 至少5GB可用空间（包含数据和模型）

### 软件要求
- **操作系统：** Windows 10/11, Linux (Ubuntu 18.04+), macOS
- **Python：** 3.7-3.9（推荐3.8）
- **CUDA：** 如使用GPU，需要CUDA 10.2或11.x版本

## 环境配置步骤

### 1. 安装Python和包管理器

#### Windows用户
1. 从 [Python官网](https://www.python.org/downloads/) 下载Python 3.8
2. 安装时勾选"Add Python to PATH"
3. 验证安装：
```cmd
python --version
pip --version
```

#### Linux用户
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.8 python3-pip python3-venv

# 验证安装
python3 --version
pip3 --version
```

#### macOS用户
```bash
# 使用Homebrew
brew install python@3.8

# 验证安装
python3 --version
pip3 --version
```

### 2. 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv smv_env

# 激活虚拟环境
# Windows
smv_env\Scripts\activate
# Linux/macOS
source smv_env/bin/activate

# 验证虚拟环境
which python  # Linux/macOS
where python  # Windows
```

### 3. 安装依赖包

#### 核心依赖
```bash
# 更新pip
pip install --upgrade pip

# 安装PyTorch（根据您的CUDA版本选择）
# CPU版本
pip install torch torchvision torchaudio

# CUDA 11.8版本（推荐）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# CUDA 12.1版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

#### 方法一：使用requirements.txt（推荐）
```bash
# 安装所有依赖
pip install -r requirements.txt
```

#### 方法二：手动安装
```bash
pip install numpy scipy matplotlib
pip install cmasher  # 用于颜色映射
```

### 4. 验证PyTorch安装

创建测试脚本验证安装：
```python
import torch
import numpy as np

print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"GPU数量: {torch.cuda.device_count()}")
    print(f"当前GPU: {torch.cuda.get_device_name(0)}")

# 简单测试
x = torch.randn(3, 3)
print(f"测试张量: {x}")
```

如果能正常输出信息且无错误，说明PyTorch安装成功。

## 数据准备

### 1. 下载预训练模型和数据
1. 访问：https://doi.org/10.7910/DVN/SECUFD
2. 下载以下文件：
   - `SMV_pretrained_weights_mousebrain.pth.tar`（小鼠脑部模型）
   - `SMV_pretrained_weights_CAM.pth.tar`（CAM模型）
   - 示例IQ数据文件（如：`dirfilt_IQData_20220502T105116_3.mat`）

### 2. 组织数据目录
建议的目录结构：
```
SR_microvessel_velocimetry/
├── models_pretrained/
│   ├── SMV_pretrained_weights_mousebrain.pth.tar
│   └── SMV_pretrained_weights_CAM.pth.tar
├── data/
│   └── dirfilt_IQData_20220502T105116_3.mat
└── output/
    └── (推理结果将保存在这里)
```

## 运行项目

### 1. 快速测试
使用提供的示例脚本：
```bash
# 修改example.sh中的路径
# 将路径改为您的实际路径
python inference.py \
    -m 'SMV_pretrained_weights_mousebrain.pth.tar' \
    -mf './models_pretrained/' \
    -fn 'dirfilt_IQData_20220502T105116_3.mat' \
    -ff './data/' \
    -sp './output/example_out.mat' \
    -w 16 -bd 1 -df 1
```

### 2. 参数说明
- `-m`: 模型文件名
- `-mf`: 模型文件夹路径
- `-fn`: 输入IQ数据文件名
- `-ff`: 输入数据文件夹路径
- `-sp`: 输出文件保存路径
- `-w`: 窗口大小（帧数）
- `-s`: 步长（默认等于窗口大小）
- `-bd`: 是否生成双向流图（0或1）
- `-df`: 输入数据是否经过方向滤波（0或1）

### 3. 查看结果
推理完成后，结果将保存为MATLAB格式文件（.mat），可以使用以下方式查看：

#### Python查看
```python
from scipy.io import loadmat
import matplotlib.pyplot as plt

# 加载结果
result = loadmat('output/example_out.mat')
print("结果包含的变量:", result.keys())

# 可视化（根据实际变量名调整）
plt.figure(figsize=(10, 8))
plt.imshow(result['velocity_map'])  # 示例变量名
plt.colorbar()
plt.title('血流速度图')
plt.show()
```

#### MATLAB查看
```matlab
load('output/example_out.mat');
imagesc(velocity_map);  % 根据实际变量名调整
colorbar;
title('血流速度图');
```

## 常见问题解决

### 1. CUDA相关问题
**问题：** `RuntimeError: CUDA out of memory`
**解决：**
```python
# 在inference.py开头添加
import torch
torch.cuda.empty_cache()

# 或减小批处理大小/窗口大小
```

**问题：** CUDA版本不匹配
**解决：** 重新安装对应CUDA版本的PyTorch

### 2. 依赖包问题
**问题：** `ModuleNotFoundError`
**解决：**
```bash
# 确保在正确的虚拟环境中
pip install 缺失的包名
```

### 3. 数据加载问题
**问题：** 无法加载.mat文件
**解决：**
- 确保scipy版本兼容：`pip install scipy>=1.7.0`
- 检查文件路径是否正确
- 确认.mat文件格式（MATLAB v7.3格式可能需要h5py）

### 4. 内存不足
**解决方案：**
- 减小窗口大小（-w参数）
- 增加步长以减少重叠
- 使用CPU模式（虽然较慢）

## 性能优化建议

### 1. GPU优化
```python
# 在代码中添加
torch.backends.cudnn.benchmark = True  # 加速卷积运算
torch.backends.cudnn.deterministic = False  # 允许非确定性算法
```

### 2. 数据预处理
- 预先将数据转换为合适的格式
- 使用适当的数据类型（float32而非float64）

### 3. 批处理
- 如果内存允许，可以增加批处理大小
- 对于大数据集，考虑分块处理

## 进阶使用

### 1. 自定义数据
如果使用自己的超声数据：
1. 确保数据格式为 `(height, width, n_frames)`
2. 修改 `utils/load_iqdata.py` 中的加载函数
3. 调整预处理步骤以匹配训练数据的分布

### 2. 模型微调
如果需要在自己的数据上微调模型：
1. 准备标注数据
2. 修改训练脚本（需要额外实现）
3. 调整学习率和训练参数

### 3. 实时处理
对于实时应用：
1. 优化数据加载管道
2. 使用多线程处理
3. 考虑使用TensorRT等推理加速框架

## 技术支持

如果遇到问题：
1. 检查本文档的常见问题部分
2. 确认环境配置是否正确
3. 查看项目的GitHub Issues
4. 联系项目维护者

## 更新日志

- **v1.0**: 初始版本发布
- 后续版本更新将在此记录

---

**注意：** 本指南基于项目当前状态编写，如有更新请参考最新版本的文档。
