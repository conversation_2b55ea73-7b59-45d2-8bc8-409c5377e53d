# 项目环境配置与复现指南

本文档详细说明如何配置环境并成功运行超分辨率微泡测速项目。

## 系统要求

### 硬件要求
- **推荐配置：** NVIDIA GPU（支持CUDA）用于加速训练和推理
- **最低配置：** CPU（性能较慢但可运行）
- **内存：** 建议8GB以上RAM
- **存储：** 至少5GB可用空间（包含数据和模型）

### 软件要求
- **操作系统：** Windows 10/11（主要支持）, Linux (Ubuntu 18.04+), macOS
- **Python：** 3.7-3.9（推荐3.8）
- **CUDA：** 如使用GPU，需要CUDA 11.8或12.1版本（Windows推荐）

## 环境配置步骤

### 1. 安装Python和包管理器

#### Windows用户（主要教程）
1. **下载Python**
   - 访问 [Python官网](https://www.python.org/downloads/windows/)
   - 下载Python 3.8.x版本（推荐3.8.10）
   - 选择"Windows installer (64-bit)"

2. **安装Python**
   - 运行下载的安装程序
   - **重要：勾选"Add Python to PATH"**
   - 选择"Install Now"或"Customize installation"
   - 如果选择自定义，确保勾选"pip"和"Add Python to environment variables"

3. **验证安装**
   - 打开命令提示符（Win+R，输入cmd）
   - 运行以下命令：
   ```cmd
   python --version
   pip --version
   ```
   - 应该显示Python 3.8.x和pip版本信息

4. **可选：安装Git**
   - 从 [Git官网](https://git-scm.com/download/win) 下载Git for Windows
   - 安装后可以使用Git Bash（推荐）或命令提示符

#### Linux/macOS用户
<details>
<summary>点击展开Linux/macOS安装说明</summary>

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install python3.8 python3-pip python3-venv
python3 --version && pip3 --version
```

**macOS:**
```bash
# 使用Homebrew
brew install python@3.8
python3 --version && pip3 --version
```
</details>

### 2. 创建虚拟环境

#### Windows环境（主要教程）
1. **打开命令提示符或PowerShell**
   - 按Win+R，输入`cmd`或`powershell`
   - 或者在项目文件夹中按Shift+右键，选择"在此处打开PowerShell窗口"

2. **创建虚拟环境**
   ```cmd
   # 创建虚拟环境
   python -m venv smv_env
   ```

3. **激活虚拟环境**
   ```cmd
   # 激活虚拟环境
   smv_env\Scripts\activate
   ```

   激活成功后，命令行前面会显示`(smv_env)`

4. **验证虚拟环境**
   ```cmd
   where python
   where pip
   ```
   应该显示虚拟环境中的Python路径

#### Linux/macOS环境
<details>
<summary>点击展开Linux/macOS虚拟环境说明</summary>

```bash
# 创建虚拟环境
python3 -m venv smv_env

# 激活虚拟环境
source smv_env/bin/activate

# 验证虚拟环境
which python
which pip
```
</details>

### 3. 安装依赖包

#### Windows环境安装步骤

1. **确保虚拟环境已激活**
   ```cmd
   # 如果没有激活，先激活虚拟环境
   smv_env\Scripts\activate
   ```

2. **更新pip**
   ```cmd
   python -m pip install --upgrade pip
   ```

3. **安装PyTorch（Windows推荐方式）**

   **选择A：有NVIDIA GPU（推荐）**
   ```cmd
   # CUDA 11.8版本（推荐，兼容性好）
   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
   ```

   **选择B：仅CPU版本**
   ```cmd
   # CPU版本（如果没有NVIDIA GPU）
   pip install torch torchvision torchaudio
   ```

4. **安装其他依赖**

   **方法一：使用requirements.txt（推荐）**
   ```cmd
   pip install -r requirements.txt
   ```

   **方法二：手动安装**
   ```cmd
   pip install numpy scipy matplotlib cmasher
   ```

#### 其他平台安装
<details>
<summary>点击展开Linux/macOS安装说明</summary>

```bash
# 更新pip
pip install --upgrade pip

# PyTorch安装（Linux/macOS）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 其他依赖
pip install -r requirements.txt
```
</details>

### 4. 验证安装（Windows环境）

1. **创建测试脚本**
   - 在项目目录创建文件`test_install.py`
   - 复制以下内容：
   ```python
   import torch
   import numpy as np
   import matplotlib.pyplot as plt
   import scipy

   print("=" * 50)
   print("环境测试报告")
   print("=" * 50)

   print(f"Python版本: {torch.__version__ if hasattr(torch, '__version__') else 'N/A'}")
   print(f"PyTorch版本: {torch.__version__}")
   print(f"NumPy版本: {np.__version__}")
   print(f"SciPy版本: {scipy.__version__}")

   print(f"\nCUDA可用: {torch.cuda.is_available()}")
   if torch.cuda.is_available():
       print(f"CUDA版本: {torch.version.cuda}")
       print(f"GPU数量: {torch.cuda.device_count()}")
       for i in range(torch.cuda.device_count()):
           print(f"GPU {i}: {torch.cuda.get_device_name(i)}")

   # 简单测试
   x = torch.randn(3, 3)
   print(f"\n测试张量创建: 成功")
   print("所有依赖安装正确！")
   ```

2. **运行测试**
   ```cmd
   python test_install.py
   ```

3. **预期输出**
   - 应该显示所有包的版本信息
   - 如果有GPU，会显示CUDA信息
   - 最后显示"所有依赖安装正确！"

## 数据准备

### 1. 下载预训练模型和数据
1. 访问：https://doi.org/10.7910/DVN/SECUFD
2. 下载以下文件：
   - `SMV_pretrained_weights_mousebrain.pth.tar`（小鼠脑部模型）
   - `SMV_pretrained_weights_CAM.pth.tar`（CAM模型）
   - 示例IQ数据文件（如：`dirfilt_IQData_20220502T105116_3.mat`）

### 2. 组织数据目录
建议的目录结构：
```
SR_microvessel_velocimetry/
├── models_pretrained/
│   ├── SMV_pretrained_weights_mousebrain.pth.tar
│   └── SMV_pretrained_weights_CAM.pth.tar
├── data/
│   └── dirfilt_IQData_20220502T105116_3.mat
└── output/
    └── (推理结果将保存在这里)
```

## 运行项目

### 1. Windows环境运行步骤

1. **确保虚拟环境已激活**
   ```cmd
   smv_env\Scripts\activate
   ```

2. **创建必要的文件夹**
   ```cmd
   mkdir models_pretrained
   mkdir data
   mkdir output
   ```

3. **运行推理脚本**
   ```cmd
   python inference.py ^
       -m "SMV_pretrained_weights_mousebrain.pth.tar" ^
       -mf ".\models_pretrained\" ^
       -fn "dirfilt_IQData_20220502T105116_3.mat" ^
       -ff ".\data\" ^
       -sp ".\output\example_out.mat" ^
       -w 16 -bd 1 -df 1
   ```

   **注意：** Windows命令行使用`^`进行换行，路径使用反斜杠`\`

4. **或者使用PowerShell（推荐）**
   ```powershell
   python inference.py `
       -m "SMV_pretrained_weights_mousebrain.pth.tar" `
       -mf ".\models_pretrained\" `
       -fn "dirfilt_IQData_20220502T105116_3.mat" `
       -ff ".\data\" `
       -sp ".\output\example_out.mat" `
       -w 16 -bd 1 -df 1
   ```

   **注意：** PowerShell使用反引号`` ` ``进行换行

### 2. 参数说明
- `-m`: 模型文件名
- `-mf`: 模型文件夹路径
- `-fn`: 输入IQ数据文件名
- `-ff`: 输入数据文件夹路径
- `-sp`: 输出文件保存路径
- `-w`: 窗口大小（帧数）
- `-s`: 步长（默认等于窗口大小）
- `-bd`: 是否生成双向流图（0或1）
- `-df`: 输入数据是否经过方向滤波（0或1）

### 3. 查看结果
推理完成后，结果将保存为MATLAB格式文件（.mat），可以使用以下方式查看：

#### Python查看
```python
from scipy.io import loadmat
import matplotlib.pyplot as plt

# 加载结果
result = loadmat('output/example_out.mat')
print("结果包含的变量:", result.keys())

# 可视化（根据实际变量名调整）
plt.figure(figsize=(10, 8))
plt.imshow(result['velocity_map'])  # 示例变量名
plt.colorbar()
plt.title('血流速度图')
plt.show()
```

#### MATLAB查看
```matlab
load('output/example_out.mat');
imagesc(velocity_map);  % 根据实际变量名调整
colorbar;
title('血流速度图');
```

## 常见问题解决

### Windows环境常见问题

### 1. Python/pip相关问题
**问题：** `'python' 不是内部或外部命令`
**解决：**
- 重新安装Python，确保勾选"Add Python to PATH"
- 或手动添加Python到系统PATH环境变量
- 尝试使用`py`命令代替`python`

**问题：** `pip install`速度很慢
**解决：**
```cmd
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 2. 虚拟环境问题
**问题：** 无法激活虚拟环境
**解决：**
```cmd
# 如果是PowerShell执行策略问题
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 然后重新激活
smv_env\Scripts\activate
```

### 3. CUDA相关问题
**问题：** `RuntimeError: CUDA out of memory`
**解决：**
- 减小窗口大小：`-w 8`（默认16）
- 关闭其他占用GPU的程序
- 使用CPU模式（虽然较慢）

**问题：** CUDA版本不匹配
**解决：**
```cmd
# 卸载当前PyTorch
pip uninstall torch torchvision torchaudio

# 重新安装CPU版本
pip install torch torchvision torchaudio

# 或安装对应CUDA版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 4. 路径和文件问题（Windows特有）
**问题：** 路径包含空格或中文字符
**解决：**
```cmd
# 使用双引号包围路径
python inference.py -mf "C:\Users\<USER>\Desktop\models\"

# 或避免使用包含空格/中文的路径
```

**问题：** 反斜杠路径问题
**解决：**
```cmd
# 使用双反斜杠或正斜杠
-mf ".\models\\"
# 或
-mf "./models/"
```

### 5. 依赖包问题
**问题：** `ModuleNotFoundError`
**解决：**
```cmd
# 确保在正确的虚拟环境中
smv_env\Scripts\activate
pip install 缺失的包名
```

**问题：** Visual C++ 编译错误
**解决：**
- 安装 Microsoft C++ Build Tools
- 或使用预编译的wheel包：`pip install --only-binary=all 包名`

### 6. 数据加载问题
**问题：** 无法加载.mat文件
**解决：**
```cmd
# 确保scipy版本兼容
pip install scipy>=1.7.0

# 如果是MATLAB v7.3格式
pip install h5py
```

### 7. 内存不足
**解决方案：**
- 减小窗口大小：`-w 8`
- 增加步长：`-s 8`
- 使用CPU模式（添加环境变量）：
```cmd
set CUDA_VISIBLE_DEVICES=-1
python inference.py ...
```

## 性能优化建议

### 1. GPU优化
```python
# 在代码中添加
torch.backends.cudnn.benchmark = True  # 加速卷积运算
torch.backends.cudnn.deterministic = False  # 允许非确定性算法
```

### 2. 数据预处理
- 预先将数据转换为合适的格式
- 使用适当的数据类型（float32而非float64）

### 3. 批处理
- 如果内存允许，可以增加批处理大小
- 对于大数据集，考虑分块处理

## 进阶使用

### 1. 自定义数据
如果使用自己的超声数据：
1. 确保数据格式为 `(height, width, n_frames)`
2. 修改 `utils/load_iqdata.py` 中的加载函数
3. 调整预处理步骤以匹配训练数据的分布

### 2. 模型微调
如果需要在自己的数据上微调模型：
1. 准备标注数据
2. 修改训练脚本（需要额外实现）
3. 调整学习率和训练参数

### 3. 实时处理
对于实时应用：
1. 优化数据加载管道
2. 使用多线程处理
3. 考虑使用TensorRT等推理加速框架

## 技术支持

如果遇到问题：
1. 检查本文档的常见问题部分
2. 确认环境配置是否正确
3. 查看项目的GitHub Issues
4. 联系项目维护者

## 更新日志

- **v1.0**: 初始版本发布
- 后续版本更新将在此记录

---

**注意：** 本指南基于项目当前状态编写，如有更新请参考最新版本的文档。
