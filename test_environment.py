#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境测试脚本
用于验证项目运行环境是否正确配置

运行方法：
python test_environment.py
"""

import sys
import importlib

def test_python_version():
    """测试Python版本"""
    print("=" * 50)
    print("Python版本检查")
    print("=" * 50)
    
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 7:
        print("✓ Python版本符合要求 (>= 3.7)")
        return True
    else:
        print("✗ Python版本不符合要求，需要Python 3.7或更高版本")
        return False

def test_package_import(package_name, display_name=None):
    """测试包导入"""
    if display_name is None:
        display_name = package_name
    
    try:
        module = importlib.import_module(package_name)
        if hasattr(module, '__version__'):
            version = module.__version__
            print(f"✓ {display_name}: {version}")
        else:
            print(f"✓ {display_name}: 已安装")
        return True
    except ImportError:
        print(f"✗ {display_name}: 未安装")
        return False

def test_torch_cuda():
    """测试PyTorch和CUDA"""
    print("\n" + "=" * 50)
    print("PyTorch和CUDA检查")
    print("=" * 50)
    
    try:
        import torch
        print(f"✓ PyTorch版本: {torch.__version__}")
        
        # 检查CUDA
        if torch.cuda.is_available():
            print(f"✓ CUDA可用: {torch.version.cuda}")
            print(f"✓ GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"  - GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("⚠ CUDA不可用，将使用CPU模式（速度较慢）")
        
        # 简单测试
        x = torch.randn(3, 3)
        if torch.cuda.is_available():
            x_gpu = x.cuda()
            print("✓ GPU张量操作测试通过")
        
        print("✓ PyTorch基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ PyTorch测试失败: {e}")
        return False

def test_project_modules():
    """测试项目模块"""
    print("\n" + "=" * 50)
    print("项目模块检查")
    print("=" * 50)
    
    modules_to_test = [
        ('models.lstm_unet', 'LSTM-UNet模型'),
        ('models.convlstm', '卷积LSTM'),
        ('utils.load_iqdata', '数据加载模块'),
        ('utils.smv_process_iq', 'SMV处理模块')
    ]
    
    all_passed = True
    for module_name, display_name in modules_to_test:
        try:
            importlib.import_module(module_name)
            print(f"✓ {display_name}")
        except ImportError as e:
            print(f"✗ {display_name}: {e}")
            all_passed = False
    
    return all_passed

def main():
    """主测试函数"""
    print("超分辨率微泡测速项目 - 环境测试")
    print("=" * 50)
    
    # 测试结果列表
    results = []
    
    # Python版本测试
    results.append(test_python_version())
    
    # 依赖包测试
    print("\n" + "=" * 50)
    print("依赖包检查")
    print("=" * 50)
    
    packages = [
        ('numpy', 'NumPy'),
        ('scipy', 'SciPy'),
        ('matplotlib', 'Matplotlib'),
        ('cmasher', 'CMasher')
    ]
    
    for package, display_name in packages:
        results.append(test_package_import(package, display_name))
    
    # PyTorch和CUDA测试
    results.append(test_torch_cuda())
    
    # 项目模块测试
    results.append(test_project_modules())
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("🎉 所有测试通过！环境配置正确。")
        print("\n下一步：")
        print("1. 下载预训练模型和数据")
        print("2. 运行 inference.py 进行推理")
        print("3. 查看 README_SETUP.md 获取详细说明")
    else:
        print(f"⚠ {total - passed} 项测试失败，请检查环境配置。")
        print("\n建议：")
        print("1. 检查Python版本是否符合要求")
        print("2. 运行 pip install -r requirements.txt 安装依赖")
        print("3. 查看 README_SETUP.md 获取详细配置说明")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
