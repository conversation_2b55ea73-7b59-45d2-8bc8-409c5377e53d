#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_sparse_broadcast_to_ops.h>

namespace at {


// aten::_sparse_broadcast_to(Tensor(a) self, int[] size) -> Tensor(a)
inline at::Tensor _sparse_broadcast_to(const at::Tensor & self, at::IntArrayRef size) {
    return at::_ops::_sparse_broadcast_to::call(self, size);
}

}
