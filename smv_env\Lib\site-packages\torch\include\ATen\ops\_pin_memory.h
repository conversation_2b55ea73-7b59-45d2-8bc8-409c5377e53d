#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_pin_memory_ops.h>

namespace at {


// aten::_pin_memory(Tensor self, Device? device=None) -> Tensor
inline at::Tensor _pin_memory(const at::Tensor & self, ::std::optional<at::Device> device=::std::nullopt) {
    return at::_ops::_pin_memory::call(self, device);
}

// aten::_pin_memory.out(Tensor self, Device? device=None, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & _pin_memory_out(at::Tensor & out, const at::Tensor & self, ::std::optional<at::Device> device=::std::nullopt) {
    return at::_ops::_pin_memory_out::call(self, device, out);
}
// aten::_pin_memory.out(Tensor self, Device? device=None, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & _pin_memory_outf(const at::Tensor & self, ::std::optional<at::Device> device, at::Tensor & out) {
    return at::_ops::_pin_memory_out::call(self, device, out);
}

}
