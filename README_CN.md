# 无定位超分辨率微泡测速系统

> **主要文档** | [English Version](README.md)

## 项目简介
本项目是基于长短期记忆神经网络的超分辨率微泡测速技术的Python实现。该系统能够对超声数据进行高精度的血流速度测量，无需传统的微泡定位步骤。

**核心技术：**
- LSTM-UNet深度学习模型，用于超分辨率血流重建
- 基于PyTorch的卷积LSTM实现
- 支持方向性滤波的IQ数据处理

**技术来源：**
- U-Net模型改编自：https://github.com/milesial/Pytorch-UNet
- 卷积LSTM实现参考：https://github.com/ndrplz/ConvLSTM_pytorch/blob/master/convlstm.py

## 快速开始

### 1. 环境配置（Windows环境）
```cmd
# 克隆项目（如果有Git）
git clone <项目地址>
cd SR_microvessel_velocimetry

# 或者直接下载项目压缩包并解压

# 创建虚拟环境
python -m venv smv_env

# 激活虚拟环境
smv_env\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

**其他平台：** 详见下方"详细环境配置"部分

### 2. 下载数据和模型
从以下链接下载预训练模型和示例数据：
**数据下载地址：** https://doi.org/10.7910/DVN/SECUFD

### 3. 运行示例（Windows环境）
```cmd
# 创建必要文件夹
mkdir models_pretrained data output

# 运行推理（命令提示符）
python inference.py ^
    -m "SMV_pretrained_weights_mousebrain.pth.tar" ^
    -mf ".\models_pretrained\" ^
    -fn "dirfilt_IQData_20220502T105116_3.mat" ^
    -ff ".\data\" ^
    -sp ".\output\example_out.mat" ^
    -w 16 -bd 1 -df 1
```

**PowerShell用户：** 将`^`替换为`` ` ``（反引号）

### 4. Windows一键运行（推荐）
对于Windows用户，我们提供了一键运行脚本：
```cmd
# 双击运行或在命令行执行
run_example_windows.bat
```

该脚本会自动：
- 检查Python环境
- 创建和激活虚拟环境
- 安装依赖包
- 检查模型和数据文件
- 运行推理程序

**详细配置说明请参考下方"详细环境配置"部分**

## 使用方法

### 数据加载
我们在 [load_iqdata.py](utils/load_iqdata.py) 中提供了数据加载函数，用于加载上述链接中的超声数据。

**数据处理说明：**
- 如果加载的是预方向滤波数据，SMV处理将使用IQ数据估计血流方向
- 否则将基于滤波数据的幅度提供上下血流方向图

### SMV处理
使用 [inference.py](inference.py) 进行SMV处理的示例代码。

**命令行参数：**
```
inference.py [-h] -m MODEL -mf MODELFOLDER -fn FILENAME -ff FILEFOLDER
             -sp SAVEPATH [-w WINDOWSIZE] [-s STEPSIZE]
             [-rw ROLLINGWINDOW] [-bd BIDIRECTIONAL]
             [-df DIRECTIONALFILTER]
```

使用 `-h` 或 `--help` 选项查看各参数的详细说明。

**示例命令（16帧块，无重叠，启用方向滤波）：**
```bash
python3 inference.py -m 'SMV_pretrained_weights_mousebrain.pth.tar' \
                     -mf '/home/<USER>/Desktop/' \
                     -fn 'dirfilt_IQData_20220502T105116_3.mat' \
                     -ff '/home/<USER>/Desktop/Deep-SMV/Experiments/MouseBrain/mouse_ecg_502/' \
                     -sp 'example_out.mat' \
                     -w 16 -bd 1 -df 1
```

**自定义数据注意事项：**
如果使用自己的数据和数据加载函数，请注意SMV处理代码期望输入的超声图像堆栈形状为 `(height, width, n_frames)`。

## 项目结构
```
├── README_CN.md           # 项目说明文档（中文，主要文档）
├── README.md              # 项目说明文档（英文）
├── requirements.txt       # Python依赖包列表
├── inference.py           # 主要推理脚本
├── example.sh            # 示例运行脚本（Linux/macOS）
├── run_example_windows.bat # Windows一键运行脚本
├── models/               # 模型定义
│   ├── lstm_unet.py      # LSTM-UNet模型
│   ├── lstm_unet_fast.py # 快速版本LSTM-UNet
│   └── convlstm.py       # 卷积LSTM实现
├── utils/                # 工具函数
│   ├── load_iqdata.py    # 数据加载函数
│   └── smv_process_iq.py # SMV处理函数
└── tests/                # 测试代码
    ├── multi_thread_test.py
    ├── tcp_client_test.py
    └── tcp_server_test.m
```

## 详细环境配置

### 系统要求

#### 硬件要求
- **推荐配置：** NVIDIA GPU（支持CUDA）用于加速训练和推理
- **最低配置：** CPU（性能较慢但可运行）
- **内存：** 建议8GB以上RAM
- **存储：** 至少5GB可用空间（包含数据和模型）

#### 软件要求
- **操作系统：** Windows 10/11（主要支持）, Linux (Ubuntu 18.04+), macOS
- **Python：** 3.7-3.9（推荐3.8）
- **CUDA：** 如使用GPU，需要CUDA 11.8或12.1版本（Windows推荐）

### 详细安装步骤

#### Windows用户（主要教程）

**1. 安装Python**
1. 访问 [Python官网](https://www.python.org/downloads/windows/)
2. 下载Python 3.8.x版本（推荐3.8.10）
3. 运行安装程序，**重要：勾选"Add Python to PATH"**
4. 验证安装：
   ```cmd
   python --version
   pip --version
   ```

**2. 创建虚拟环境**
```cmd
# 创建虚拟环境
python -m venv smv_env

# 激活虚拟环境
smv_env\Scripts\activate

# 验证虚拟环境
where python
```

**3. 安装依赖包**
```cmd
# 更新pip
python -m pip install --upgrade pip

# 安装PyTorch（有GPU推荐）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 或CPU版本（无GPU）
pip install torch torchvision torchaudio

# 安装其他依赖
pip install -r requirements.txt
```

**4. 验证安装**
创建测试文件`test_install.py`：
```python
import torch
import numpy as np

print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")
print("环境配置成功！")
```

运行测试：
```cmd
python test_install.py
```

#### Linux/macOS用户

<details>
<summary>点击展开Linux/macOS详细安装说明</summary>

**Linux (Ubuntu/Debian):**
```bash
# 安装Python
sudo apt update
sudo apt install python3.8 python3-pip python3-venv

# 创建虚拟环境
python3 -m venv smv_env
source smv_env/bin/activate

# 安装依赖
pip install --upgrade pip
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install -r requirements.txt
```

**macOS:**
```bash
# 使用Homebrew安装Python
brew install python@3.8

# 创建虚拟环境
python3 -m venv smv_env
source smv_env/bin/activate

# 安装依赖
pip install --upgrade pip
pip install torch torchvision torchaudio
pip install -r requirements.txt
```
</details>

### 数据准备

1. **下载预训练模型和数据**
   - 访问：https://doi.org/10.7910/DVN/SECUFD
   - 下载文件：
     - `SMV_pretrained_weights_mousebrain.pth.tar`（小鼠脑部模型）
     - `SMV_pretrained_weights_CAM.pth.tar`（CAM模型）
     - 示例IQ数据文件（如：`dirfilt_IQData_20220502T105116_3.mat`）

2. **组织数据目录**
   ```
   SR_microvessel_velocimetry/
   ├── models_pretrained/
   │   ├── SMV_pretrained_weights_mousebrain.pth.tar
   │   └── SMV_pretrained_weights_CAM.pth.tar
   ├── data/
   │   └── dirfilt_IQData_20220502T105116_3.mat
   └── output/
       └── (推理结果将保存在这里)
   ```

### 常见问题解决

#### Windows环境常见问题

**问题：** `'python' 不是内部或外部命令`
**解决：**
- 重新安装Python，确保勾选"Add Python to PATH"
- 尝试使用`py`命令代替`python`

**问题：** 无法激活虚拟环境（PowerShell）
**解决：**
```cmd
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
smv_env\Scripts\activate
```

**问题：** CUDA内存不足
**解决：**
- 减小窗口大小：`-w 8`（默认16）
- 使用CPU模式：`set CUDA_VISIBLE_DEVICES=-1`

**问题：** 路径包含空格或中文
**解决：**
```cmd
# 使用双引号包围路径
python inference.py -mf "C:\Users\<USER>\Desktop\models\"
```

**问题：** pip安装速度慢
**解决：**
```cmd
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 技术特点
- **无需定位：** 直接从超声数据重建高分辨率血流图，无需传统的微泡定位步骤
- **深度学习：** 使用LSTM-UNet架构，能够捕获时序信息和空间特征
- **高精度：** 实现超分辨率血流测速，精度优于传统方法
- **实时处理：** 支持GPU加速，可进行实时或近实时处理

## 预训练模型和数据
项目提供两个预训练模型（CAM模型和小鼠脑部模型）以及示例超声数据，可从以下链接获取：
https://doi.org/10.7910/DVN/SECUFD

## 引用
如果您在研究中使用了本项目，请引用相关论文。

## 许可证
请查看项目许可证文件了解使用条款。

---

## 文档导航
- **[README_CN.md](README_CN.md)** - 中文主要文档（当前文档）
- **[README.md](README.md)** - 英文版本文档
