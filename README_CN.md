# 无定位超分辨率微泡测速系统

> **主要文档** | [English Version](README.md) | [环境配置指南](README_SETUP.md)

## 项目简介
本项目是基于长短期记忆神经网络的超分辨率微泡测速技术的Python实现。该系统能够对超声数据进行高精度的血流速度测量，无需传统的微泡定位步骤。

**核心技术：**
- LSTM-UNet深度学习模型，用于超分辨率血流重建
- 基于PyTorch的卷积LSTM实现
- 支持方向性滤波的IQ数据处理

**技术来源：**
- U-Net模型改编自：https://github.com/milesial/Pytorch-UNet
- 卷积LSTM实现参考：https://github.com/ndrplz/ConvLSTM_pytorch/blob/master/convlstm.py

## 快速开始

### 1. 环境配置
```bash
# 克隆项目
git clone <项目地址>
cd SR_microvessel_velocimetry

# 创建虚拟环境
python -m venv smv_env
source smv_env/bin/activate  # Linux/macOS
# 或 smv_env\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 下载数据和模型
从以下链接下载预训练模型和示例数据：
**数据下载地址：** https://doi.org/10.7910/DVN/SECUFD

### 3. 运行示例
```bash
python inference.py -m 'SMV_pretrained_weights_mousebrain.pth.tar' \
                    -mf './models_pretrained/' \
                    -fn 'dirfilt_IQData_20220502T105116_3.mat' \
                    -ff './data/' \
                    -sp './output/example_out.mat' \
                    -w 16 -bd 1 -df 1
```

**详细配置说明请参考：** [环境配置指南](README_SETUP.md)

## 使用方法

### 数据加载
我们在 [load_iqdata.py](utils/load_iqdata.py) 中提供了数据加载函数，用于加载上述链接中的超声数据。

**数据处理说明：**
- 如果加载的是预方向滤波数据，SMV处理将使用IQ数据估计血流方向
- 否则将基于滤波数据的幅度提供上下血流方向图

### SMV处理
使用 [inference.py](inference.py) 进行SMV处理的示例代码。

**命令行参数：**
```
inference.py [-h] -m MODEL -mf MODELFOLDER -fn FILENAME -ff FILEFOLDER
             -sp SAVEPATH [-w WINDOWSIZE] [-s STEPSIZE]
             [-rw ROLLINGWINDOW] [-bd BIDIRECTIONAL]
             [-df DIRECTIONALFILTER]
```

使用 `-h` 或 `--help` 选项查看各参数的详细说明。

**示例命令（16帧块，无重叠，启用方向滤波）：**
```bash
python3 inference.py -m 'SMV_pretrained_weights_mousebrain.pth.tar' \
                     -mf '/home/<USER>/Desktop/' \
                     -fn 'dirfilt_IQData_20220502T105116_3.mat' \
                     -ff '/home/<USER>/Desktop/Deep-SMV/Experiments/MouseBrain/mouse_ecg_502/' \
                     -sp 'example_out.mat' \
                     -w 16 -bd 1 -df 1
```

**自定义数据注意事项：**
如果使用自己的数据和数据加载函数，请注意SMV处理代码期望输入的超声图像堆栈形状为 `(height, width, n_frames)`。

## 项目结构
```
├── README_CN.md           # 项目说明文档（中文，主要文档）
├── README.md              # 项目说明文档（英文）
├── README_SETUP.md        # 环境配置和复现指南
├── requirements.txt       # Python依赖包列表
├── inference.py           # 主要推理脚本
├── example.sh            # 示例运行脚本
├── models/               # 模型定义
│   ├── lstm_unet.py      # LSTM-UNet模型
│   ├── lstm_unet_fast.py # 快速版本LSTM-UNet
│   └── convlstm.py       # 卷积LSTM实现
├── utils/                # 工具函数
│   ├── load_iqdata.py    # 数据加载函数
│   └── smv_process_iq.py # SMV处理函数
└── tests/                # 测试代码
    ├── multi_thread_test.py
    ├── tcp_client_test.py
    └── tcp_server_test.m
```

## 技术特点
- **无需定位：** 直接从超声数据重建高分辨率血流图，无需传统的微泡定位步骤
- **深度学习：** 使用LSTM-UNet架构，能够捕获时序信息和空间特征
- **高精度：** 实现超分辨率血流测速，精度优于传统方法
- **实时处理：** 支持GPU加速，可进行实时或近实时处理

## 预训练模型和数据
项目提供两个预训练模型（CAM模型和小鼠脑部模型）以及示例超声数据，可从以下链接获取：
https://doi.org/10.7910/DVN/SECUFD

## 引用
如果您在研究中使用了本项目，请引用相关论文。

## 许可证
请查看项目许可证文件了解使用条款。

---

## 文档导航
- **[README_CN.md](README_CN.md)** - 中文主要文档（当前文档）
- **[README.md](README.md)** - 英文版本文档
- **[README_SETUP.md](README_SETUP.md)** - 详细的环境配置和复现指南
