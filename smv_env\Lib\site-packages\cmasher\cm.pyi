from matplotlib.colors import ListedColormap as LC

__all__: list[str]

CMAP_DCT = dict[str, LC]
cmap_d: CMAP_DCT
cmap_cd: dict[str, CMAP_DCT]

amber: LC
amber_r: LC
amethyst: LC
amethyst_r: LC
apple: LC
apple_r: LC
arctic: LC
arctic_r: LC
bubblegum: LC
bubblegum_r: LC
chroma: LC
chroma_r: LC
copper: LC
copper_r: LC
cosmic: LC
cosmic_r: LC
dusk: LC
dusk_r: LC
eclipse: LC
eclipse_r: LC
ember: LC
ember_r: LC
emerald: LC
emerald_r: LC
emergency: LC
emergency_r: LC
fall: LC
fall_r: LC
flamingo: LC
flamingo_r: LC
freeze: LC
freeze_r: LC
fusion: LC
fusion_r: LC
gem: LC
gem_r: LC
ghostlight: LC
ghostlight_r: LC
gothic: LC
gothic_r: LC
guppy: LC
guppy_r: LC
holly: LC
holly_r: LC
horizon: LC
horizon_r: LC
iceburn: LC
iceburn_r: LC
infinity: LC
infinity_r: LC
jungle: LC
jungle_r: LC
lavender: LC
lavender_r: LC
lilac: LC
lilac_r: LC
neon: LC
neon_r: LC
neutral: LC
neutral_r: LC
nuclear: LC
nuclear_r: LC
ocean: LC
ocean_r: LC
pepper: LC
pepper_r: LC
pride: LC
pride_r: LC
prinsenvlag: LC
prinsenvlag_r: LC
rainforest: LC
rainforest_r: LC
redshift: LC
redshift_r: LC
sapphire: LC
sapphire_r: LC
savanna: LC
savanna_r: LC
seasons: LC
seasons_r: LC
seaweed: LC
seaweed_r: LC
sepia: LC
sepia_r: LC
sunburst: LC
sunburst_r: LC
swamp: LC
swamp_r: LC
torch: LC
torch_r: LC
toxic: LC
toxic_r: LC
tree: LC
tree_r: LC
tropical: LC
tropical_r: LC
viola: LC
viola_r: LC
voltage: LC
voltage_r: LC
waterlily: LC
waterlily_r: LC
watermelon: LC
watermelon_r: LC
wildfire: LC
wildfire_r: LC
