@echo off
REM ========================================
REM Windows Conda Script - Super-Resolution Microbubble Velocimetry
REM ========================================
REM 
REM Usage:
REM Method 1: Double-click this file (Recommended)
REM Method 2: Command Prompt
REM        cd C:\path\to\project
REM        run_example_conda.bat
REM Method 3: PowerShell
REM        cd C:\path\to\project
REM        .\run_example_conda.bat
REM 
REM Note: Make sure to download model and data files to corresponding folders
REM Requires: Anaconda or Miniconda installed
REM ========================================

echo ========================================
echo Super-Resolution Microbubble Velocimetry - Conda Script
echo ========================================

REM Check if conda is available
conda --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Conda not found, please install Anaconda or Miniconda first
    echo Download Anaconda: https://www.anaconda.com/products/distribution
    echo Download Miniconda: https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

echo Conda available, version info:
conda --version

REM Check if conda environment exists
conda info --envs | findstr "smv_env" >nul 2>&1
if errorlevel 1 (
    echo Conda environment 'smv_env' does not exist, creating...
    conda create -n smv_env python=3.8 -y
    if errorlevel 1 (
        echo ERROR: Failed to create conda environment
        pause
        exit /b 1
    )
    echo Conda environment created successfully
) else (
    echo Conda environment 'smv_env' already exists
)

REM Activate conda environment
echo Activating conda environment...
call conda activate smv_env
if errorlevel 1 (
    echo ERROR: Failed to activate conda environment
    pause
    exit /b 1
)

REM Install PyTorch with conda (recommended for better compatibility)
echo Installing PyTorch with conda...
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y
if errorlevel 1 (
    echo WARNING: CUDA version installation failed, trying CPU version...
    conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
    if errorlevel 1 (
        echo ERROR: Failed to install PyTorch
        pause
        exit /b 1
    )
)

REM Install other dependencies with pip
echo Installing other dependencies...
pip install numpy scipy matplotlib cmasher -i https://pypi.tuna.tsinghua.edu.cn/simple/
if errorlevel 1 (
    echo WARNING: Some dependencies may not have installed correctly
)

REM Create necessary folders
if not exist "models_pretrained" mkdir models_pretrained
if not exist "data" mkdir data
if not exist "output" mkdir output

echo Folder structure prepared

REM Test Python and PyTorch installation
echo Testing installation...
python -c "import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}')"
if errorlevel 1 (
    echo ERROR: Python/PyTorch test failed
    pause
    exit /b 1
)

REM Check if model file exists
if not exist "models_pretrained\SMV_pretrained_weights_mousebrain.pth.tar" (
    echo ========================================
    echo WARNING: Pre-trained model file not found
    echo ========================================
    echo Please download model file from:
    echo https://doi.org/10.7910/DVN/SECUFD
    echo.
    echo Download file: SMV_pretrained_weights_mousebrain.pth.tar
    echo Place in: models_pretrained\
    echo.
    echo After downloading, run this script again.
    pause
    exit /b 1
)

REM Check if data file exists
if not exist "data\dirfilt_IQData_20220502T105116_3.mat" (
    echo ========================================
    echo WARNING: Example data file not found
    echo ========================================
    echo Please download data file from:
    echo https://doi.org/10.7910/DVN/SECUFD
    echo.
    echo Download file: dirfilt_IQData_20220502T105116_3.mat
    echo Place in: data\
    echo.
    echo After downloading, run this script again.
    pause
    exit /b 1
)

REM Run inference
echo ========================================
echo Starting inference...
echo ========================================

python inference.py ^
    -m "SMV_pretrained_weights_mousebrain.pth.tar" ^
    -mf ".\models_pretrained\" ^
    -fn "dirfilt_IQData_20220502T105116_3.mat" ^
    -ff ".\data\" ^
    -sp ".\output\example_out.mat" ^
    -w 16 -bd 1 -df 1

if errorlevel 1 (
    echo ========================================
    echo Execution failed!
    echo ========================================
    echo Possible causes:
    echo 1. Python version compatibility (using Python 3.8 in conda env)
    echo 2. Missing CUDA support (script tried both CUDA and CPU versions)
    echo 3. Memory issues (try reducing window size -w 8)
    echo 4. Model/data file corruption (re-download files)
    echo.
    echo Please check error messages above
    echo For more help, see README_CN.md
    pause
    exit /b 1
) else (
    echo ========================================
    echo Execution successful!
    echo ========================================
    echo Output file location: output\example_out.mat
    echo.
    echo You can view results using Python or MATLAB:
    echo Python: from scipy.io import loadmat; result = loadmat('output/example_out.mat')
    echo MATLAB: load('output/example_out.mat')
)

echo.
echo Press any key to exit...
pause >nul
